<!DOCTYPE html>
<html lang="en">

<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>
 * {
  margin: 0;
  padding: 0;
 }
</style>

<body>
 <div>
  <div id="content-to-print">
   <div class="print-page">
    <!-- 页眉 -->
    <div class="print-header">页头</div>
    <!-- 内容区域 -->
    <div class="print-content">
     <h1>1</h1>
     <h1>2</h1>
     <h1>3</h1>
     <h1>4</h1>
     <h1>5</h1>
     <h1>6</h1>
     <h1>7</h1>
     <h1>8</h1>
     <h1>9</h1>
     <h1>10</h1>
     <h1>11</h1>
     <h1>12</h1>
     <h1>13</h1>
     <h1>14</h1>
     <h1>15</h1>
     <h1>16</h1>
     <h1>17</h1>
     <h1>18</h1>
     <h1>19</h1>
     <h1>20</h1>
     <h1>21</h1>
     <h1>22</h1>
     <h1>23</h1>
     <h1>24</h1>
     <h1>25</h1>
     <h1>26</h1>
     <h1>27</h1>
     <h1>28</h1>
     <h1>29</h1>
     <h1>30</h1>
     <h1>31</h1>
     <h1>32</h1>
     <h1>33</h1>
     <h1>34</h1>
     <h1>35</h1>
     <h1>36</h1>
     <h1>37</h1>
     <h1>38</h1>
     <h1>39</h1>
     <h1>40</h1>
    </div>
    <!-- 页脚 -->
    <div class="print-footer">页脚</div>
   </div>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>
 <button onclick="detectPrintSettings()" class="btn">检测打印设置</button>
 <div id="print-info" style="margin: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;">
  <h3>打印信息将显示在这里</h3>
 </div>

 <script>

  // 获取打印纸大小的函数
  function getPrintPageSize() {
   // 常见纸张尺寸定义（毫米和像素）
   const pageFormats = {
    'A4': { width: 210, height: 297, widthPx: 794, heightPx: 1123 },
    'A3': { width: 297, height: 420, widthPx: 1123, heightPx: 1587 },
    'A5': { width: 148, height: 210, widthPx: 559, heightPx: 794 },
    'Letter': { width: 216, height: 279, widthPx: 816, heightPx: 1056 },
    'Legal': { width: 216, height: 356, widthPx: 816, heightPx: 1344 },
    'Tabloid': { width: 279, height: 432, widthPx: 1056, heightPx: 1632 }
   };

   // 方法1：通过创建临时打印媒体查询来检测
   const testDiv = document.createElement('div');
   testDiv.style.cssText = `
      position: absolute;
      top: -9999px;
      width: 100vw;
      height: 100vh;
      visibility: hidden;
    `;
   document.body.appendChild(testDiv);

   // 获取当前视口尺寸
   const viewportWidth = window.innerWidth;
   const viewportHeight = window.innerHeight;

   // 移除测试元素
   document.body.removeChild(testDiv);

   // 方法2：通过CSS媒体查询检测打印尺寸
   let detectedFormat = 'A4'; // 默认
   let detectedInfo = pageFormats.A4;

   // 检测最接近的纸张格式
   let minDiff = Infinity;
   for (let format in pageFormats) {
    const page = pageFormats[format];
    const widthDiff = Math.abs(viewportWidth - page.widthPx);
    const heightDiff = Math.abs(viewportHeight - page.heightPx);
    const totalDiff = widthDiff + heightDiff;

    if (totalDiff < minDiff) {
     minDiff = totalDiff;
     detectedFormat = format;
     detectedInfo = page;
    }
   }

   console.log('检测到的纸张格式:', detectedFormat);
   console.log('当前视口尺寸:', viewportWidth + 'x' + viewportHeight);
   console.log('标准尺寸:', detectedInfo.widthPx + 'x' + detectedInfo.heightPx);

   return {
    format: detectedFormat,
    ...detectedInfo,
    currentWidth: viewportWidth,
    currentHeight: viewportHeight
   };
  }

  // 根据纸张大小计算每页项目数
  function calculateItemsPerPage(pageInfo) {
   const headerHeight = 60;
   const footerHeight = 60;
   const padding = 40;
   const itemHeight = 45; // 每个h1项目的估计高度

   const availableHeight = pageInfo.currentHeight - headerHeight - footerHeight - padding;
   const itemsPerPage = Math.floor(availableHeight / itemHeight);

   console.log('页面信息:', pageInfo);
   console.log('可用高度:', availableHeight + 'px');
   console.log('计算出的每页项目数:', itemsPerPage);

   return Math.max(itemsPerPage, 3); // 最少3个项目
  }

  // 获取打印机设置信息（实验性API）
  function getPrinterSettings() {
   if ('getDisplayMedia' in navigator.mediaDevices) {
    // 这是一个实验性方法，可能不是所有浏览器都支持
    console.log('浏览器支持媒体设备API');
   }

   // 通过CSS检测打印设置
   const printMediaQuery = window.matchMedia('print');
   console.log('当前是否为打印模式:', printMediaQuery.matches);

   // 检测页面方向
   const landscapeQuery = window.matchMedia('(orientation: landscape)');
   const portraitQuery = window.matchMedia('(orientation: portrait)');

   return {
    isPrintMode: printMediaQuery.matches,
    isLandscape: landscapeQuery.matches,
    isPortrait: portraitQuery.matches,
    devicePixelRatio: window.devicePixelRatio || 1
   };
  }

  function printDocument() {
   // 获取打印纸大小和设置
   const pageInfo = getPrintPageSize();
   const printerSettings = getPrinterSettings();
   const itemsPerPage = calculateItemsPerPage(pageInfo);

   console.log('=== 打印信息 ===');
   console.log('纸张格式:', pageInfo.format);
   console.log('纸张尺寸:', pageInfo.width + 'mm x ' + pageInfo.height + 'mm');
   console.log('像素尺寸:', pageInfo.widthPx + 'px x ' + pageInfo.heightPx + 'px');
   console.log('每页项目数:', itemsPerPage);
   console.log('打印机设置:', printerSettings);

   const dom = document.getElementById('content-to-print').querySelector('.print-content');
   console.log(dom);

   // 设置打印日期
   //  document.getElementById('date').innerText = new Date().toLocaleDateString();

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `
    @media print {
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      @page {
        size: A4 portrait;
        margin: 20px;
      }

      html, body {
        height: 100%;
        font-family: Arial, sans-serif;
      }

      /* 使用 Flexbox 布局确保页脚在底部 */
      .print-page {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .print-header {
        flex: 0 0 auto; /* 不伸缩，固定高度 */
        height: 60px;
        background: white;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
      }

      .print-content {
        flex: 1 1 auto; /* 自动伸缩，占满剩余空间 */
        padding: 20px;
        overflow: hidden;
      }

      .print-content h1 {
        margin: 10px 0;
        page-break-inside: avoid;
        font-size: 18px;
      }

      .print-footer {
        flex: 0 0 auto; /* 不伸缩，固定高度 */
        height: 60px;
        background: white;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
        margin-top: auto; /* 关键：推到底部 */
      }

      /* 确保在分页时页眉页脚正确显示 */
      .print-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }

      .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }

      .print-content {
        margin-top: 80px;  /* 为页眉留出空间 */
        margin-bottom: 80px; /* 为页脚留出空间 */
        min-height: calc(100vh - 160px);
      }
    }
    `
   });
  }

  // 检测打印设置的函数
  function detectPrintSettings() {
   const pageInfo = getPrintPageSize();
   const printerSettings = getPrinterSettings();
   const itemsPerPage = calculateItemsPerPage(pageInfo);

   // 更高级的检测方法
   const advancedInfo = getAdvancedPrintInfo();

   // 显示信息
   const infoDiv = document.getElementById('print-info');
   infoDiv.innerHTML = `
      <h3>📄 检测到的打印信息</h3>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div>
          <h4>📏 纸张信息</h4>
          <p><strong>格式:</strong> ${pageInfo.format}</p>
          <p><strong>尺寸:</strong> ${pageInfo.width}mm × ${pageInfo.height}mm</p>
          <p><strong>像素:</strong> ${pageInfo.widthPx}px × ${pageInfo.heightPx}px</p>
          <p><strong>当前视口:</strong> ${pageInfo.currentWidth}px × ${pageInfo.currentHeight}px</p>
          <p><strong>每页项目数:</strong> ${itemsPerPage}</p>
        </div>
        <div>
          <h4>🖨️ 打印设置</h4>
          <p><strong>打印模式:</strong> ${printerSettings.isPrintMode ? '是' : '否'}</p>
          <p><strong>横向:</strong> ${printerSettings.isLandscape ? '是' : '否'}</p>
          <p><strong>纵向:</strong> ${printerSettings.isPortrait ? '是' : '否'}</p>
          <p><strong>设备像素比:</strong> ${printerSettings.devicePixelRatio}</p>
        </div>
      </div>
      <div style="margin-top: 15px;">
        <h4>🔍 高级信息</h4>
        <p><strong>屏幕分辨率:</strong> ${advancedInfo.screenResolution}</p>
        <p><strong>颜色深度:</strong> ${advancedInfo.colorDepth}位</p>
        <p><strong>支持的媒体查询:</strong> ${advancedInfo.supportedQueries.join(', ')}</p>
      </div>
    `;
  }

  // 获取高级打印信息
  function getAdvancedPrintInfo() {
   const screen = window.screen;
   const supportedQueries = [];

   // 检测支持的媒体查询
   const queries = [
    'print',
    '(orientation: landscape)',
    '(orientation: portrait)',
    '(min-resolution: 150dpi)',
    '(min-resolution: 300dpi)',
    '(color)',
    '(monochrome)'
   ];

   queries.forEach(query => {
    if (window.matchMedia(query).matches) {
     supportedQueries.push(query);
    }
   });

   return {
    screenResolution: screen.width + 'x' + screen.height,
    colorDepth: screen.colorDepth,
    supportedQueries: supportedQueries.length > 0 ? supportedQueries : ['无匹配']
   };
  }

  // 监听打印事件
  window.addEventListener('beforeprint', function () {
   console.log('准备打印...');
   const pageInfo = getPrintPageSize();
   console.log('打印时的页面信息:', pageInfo);
  });

  window.addEventListener('afterprint', function () {
   console.log('打印完成或取消');
  });

 </script>
</body>

</html>