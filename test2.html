<!DOCTYPE html>
<html lang="en">

<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>

</style>

<body>
 <div>
  <div id="content-to-print">
   <div class="print-header">
    <div>页头</div>
   </div>
   <div class="print-content">
    <!-- 长表格会自动分页 -->
    <h1>1</h1>
    <h1>2</h1>
    <h1>3</h1>
    <h1>4</h1>
    <h1>5</h1>
    <h1>6</h1>
    <h1>7</h1>
    <h1>8</h1>
    <h1>9</h1>
    <h1>10</h1>
    <h1>11</h1>
    <h1>12</h1>
    <h1>13</h1>
    <h1>14</h1>
    <h1>15</h1>
    <h1>16</h1>
    <h1>17</h1>
    <h1>18</h1>
    <h1>19</h1>
    <h1>20</h1>
    <h1>21</h1>
    <h1>22</h1>
    <h1>23</h1>
    <h1>24</h1>
    <h1>25</h1>
    <h1>26</h1>
    <h1>27</h1>
    <h1>28</h1>
    <h1>29</h1>
    <h1>30</h1>
    <h1>31</h1>
    <h1>32</h1>
    <h1>33</h1>
    <h1>34</h1>
    <h1>35</h1>
    <h1>36</h1>
    <h1>37</h1>
    <h1>38</h1>
    <h1>39</h1>
    <h1>40</h1>
   </div>
   <div class="print-footer">
    <div>页脚</div>
   </div>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>

  function printDocument() {
   const dom = document.getElementById('content-to-print').querySelector('.print-content');
   // console.log(dom);


   // 设置打印日期
   //  document.getElementById('date').innerText = new Date().toLocaleDateString();

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `  @media print {
    *{
    margin: 0;
    padding: 0;
    }

   @page {
    size: A4 portrait;
    margin-top: 0;
    margin-bottom: 0;
   }
   .print-header{
    width: 100%;
     height: 100%
   }
   .print-content{
    border: 1px solid red;
    height: 100%
     page-break-after: always;
   }
   
   .print-footer{
     width: 100%;
     height: 100%;
     border: 1px solid;

    
   }
  }
`
   });
  }
 </script>
</body>

</html>