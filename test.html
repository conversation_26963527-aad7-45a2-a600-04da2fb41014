<!DOCTYPE html>
<html lang="en">
<head>
  <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
  <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css">
</head>
<style>
   *{
    margin: 0;
    padding: 0;

    }
    .print-content{
      width: 794px;
    }
    </style>

<body>
 <div>
  <div id="content-to-print">
    <div class="print-page">
      <!-- 页眉 -->
      <div class="print-header" id="print-header">页头</div>
      <!-- 内容区域 -->
      <div class="print-content">
        <h1>1</h1>
        <h1>2</h1>
        <h1>3</h1>
        <h1>4</h1>
        <h1>5</h1>
        <h1>6</h1>
        <h1>7</h1>
        <h1>8</h1>
        <h1>9</h1>
        <h1>10</h1>
        <h1>11</h1>
        <h1>12</h1>
        <h1>13</h1>
        <h1>14</h1>
        <h1>15</h1>
        <h1>16</h1>
        <h1>17</h1>
        <h1>18</h1>
        <h1>19</h1>
        <h1>20</h1>
        <h1>21</h1>
        <h1>22</h1>
        <h1>23</h1>
        <h1>24</h1>
        <h1>25</h1>
        <h1>26</h1>
        <h1>27</h1>
        <h1>28</h1>
        <h1>29</h1>
        <h1>30</h1>
        <h1>31</h1>
        <h1>32</h1>
        <h1>33</h1>
        <h1>34</h1>
        <h1>35</h1>
        <h1>36</h1>
        <h1>37</h1>
        <h1>38</h1>
        <h1>39</h1>
        <h1>40</h1>
      </div>
      <!-- 页脚 -->
      <div class="print-footer" id="print-footer">页脚</div>
    </div>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>
  /**
   * 获取A4纸的尺寸
   * @returns {Object} A4纸的尺寸，包含毫米和像素单位
   */
  function getA4Size() {
      // A4纸的标准尺寸（毫米）
      const a4SizeMM = {
        width: 210,
        height: 297
      };

      // 转换为像素（假设96 DPI）
      // 1英寸 = 25.4毫米，1英寸 = 96像素
      const pixelsPerMM = 96 / 25.4;

      const a4SizePx = {
        width: Math.round(a4SizeMM.width * pixelsPerMM),
        height: Math.round(a4SizeMM.height * pixelsPerMM)
      };
      console.log('A4纸尺寸(毫米):', a4SizeMM.width + 'mm x ' + a4SizeMM.height + 'mm');
      console.log('A4纸尺寸(像素):', a4SizePx.width + 'px x ' + a4SizePx.height + 'px');
      return {
        mm: a4SizeMM,
        px: a4SizePx
      };
    }
  console.log(getA4Size());
  /**
   * 获取页面内容的高度
   * @returns {number} 页面内容的高度（像素）
   */
  function getContentHeight() {
    const content = document.querySelector('.print-content');
    return content.offsetHeight;
  }

  /**
   * 获取页眉的高度
   * @returns {number} 页眉的高度（像素）
   */
  function getHeaderHeight() {
    const header = document.querySelector('.print-header');
    return header.offsetHeight;
  }

  /**
   * 获取页脚的高度
   * @returns {number} 页脚的高度（像素）
   */
  function getFooterHeight() {
    const footer = document.querySelector('.print-footer');
    return footer.offsetHeight;
  }

  // 根据内容高度
  
  

  
  

  function printDocument() {
    const dom = document.getElementById('content-to-print').querySelector('.print-content');
    console.log(dom);
  
   // 设置打印日期
  //  document.getElementById('date').innerText = new Date().toLocaleDateString();
   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `
    @media print {
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      @page {
        size: A4 portrait;
        margin: 0;
        }
      }
    `,
   
  });
  }
 </script>
</body>
</html>