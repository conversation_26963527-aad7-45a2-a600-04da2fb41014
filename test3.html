<!DOCTYPE html>
<html lang="en">

<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>
*{
    margin: 0;
    padding: 0;
    }
</style>

<body>
 <div>
  <div id="content-to-print">
   <table>
    <thead>
     <th>
      <div class="print-header">页头</div>
     </th>
    </thead>
    <tbody>
     <tr>
      <td>
       <!-- 内容区域 -->
       <div class="print-content">
        <!-- 长表格会自动分页 -->
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
       </div>
      </td>
     </tr>
    </tbody>
    <tfoot>
     <th>
      <div class="print-footer">
       页脚
       页脚
       页脚
       页脚
      </div>
     </th>
    </tfoot>
   </table>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>
 
 /**
  * 检测屏幕的实际DPI
  * @returns {Object} 包含水平和垂直DPI的对象
  */
  function detectActualDPI() {
   // 创建一个1英寸×1英寸的测试元素
   const testDiv = document.createElement('div');
   testDiv.style.cssText = `
    width: 1in;
    height: 1in;
    position: absolute;
    left: -100%;
    top: -100%;
    visibility: hidden;
  `;
   document.body.appendChild(testDiv);

   // 测量实际像素尺寸
   const horizontalDPI = testDiv.offsetWidth;
   const verticalDPI = testDiv.offsetHeight;

   // 清理测试元素
   document.body.removeChild(testDiv);

   console.log(`检测到的DPI: 水平=${horizontalDPI}, 垂直=${verticalDPI}`);

   return {
    horizontal: horizontalDPI,
    vertical: verticalDPI,
    average: (horizontalDPI + verticalDPI) / 2
   };
  }

 /**
 * 获取A4纸的尺寸
 * @returns {Object} A4纸的尺寸，包含毫米和像素单位
 */
 function getA4Size() {
  // 获取实际DPI
  const dpi = detectActualDPI();

  // A4纸的标准尺寸（毫米）
  const a4SizeMM = {
   width: 210,
   height: 297
  };

  // 使用实际DPI转换为像素
  // 1英寸 = 25.4毫米
  const a4SizePx = {
   width: Math.round((a4SizeMM.width / 25.4) * dpi.horizontal),
   height: Math.round((a4SizeMM.height / 25.4) * dpi.vertical)
  };

  console.log('A4纸尺寸(毫米):', a4SizeMM.width + 'mm x ' + a4SizeMM.height + 'mm');
  console.log('A4纸尺寸(像素):', a4SizePx.width + 'px x ' + a4SizePx.height + 'px');
  console.log('使用实际DPI:', dpi.horizontal + ' x ' + dpi.vertical);

  return {
   mm: a4SizeMM,
   px: a4SizePx,
   dpi: dpi
  };
 }

 function printDocument() {
   const contentHeight = document.querySelector('.print-content').offsetHeight; // 内容高度
   const headerHeight = document.querySelector('.print-header').offsetHeight; // 页眉高度
   const footerHeight = document.querySelector('.print-footer').offsetHeight; // 页脚高度
   const a4Size = getA4Size(); // A4纸的尺寸
   const a4ContentHeight = a4Size.px.height - headerHeight - footerHeight; // A4可用内容高度
   const pageCount = Math.ceil(contentHeight / a4ContentHeight); // 总页数
   console.log('内容高度:', contentHeight, 'px');
   console.log('页眉高度:', headerHeight, 'px');
   console.log('页脚高度:', footerHeight, 'px');
   console.log('A4可用内容高度:', a4ContentHeight, 'px');
   console.log('计算页数:', pageCount);

   // 计算表格高度 - 使用像素单位而不是毫米
   const tableHeight = pageCount * a4Size.px.height;
   console.log('表格高度:', tableHeight, 'px');

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `@media print {
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      table {
        width: 100%;
        height: ${tableHeight - 50}px; /* 使用计算的精确高度 */
        table-layout: fixed;
      }
      @page {
        size: A4 portrait;
        margin: 0;
      }
      .print-header {
        width: 100%;
        position: sticky;
        top: 0;
      }
      .print-content {
        height: ${contentHeight}px; /* 使用实际内容高度 */
      }
      .print-footer {
        width: 100%;
        position: sticky;
        bottom: 0;
      }
      /* 控制分页 */
      .print-content h1:nth-child(${Math.floor(contentHeight / a4ContentHeight * 40)}n) {
        page-break-after: always;
      }
    }`
   });
  }
 </script>
</body>

</html>